{"system": "Generador de Imágenes IA", "helloWorld": "¡Hola Mundo!", "Describe the image you want to generate...": "Describe la imagen que quieres generar...", "appTitle": "Creación de Imágenes IA", "copyright": "Copyright © {year}, Creación de Imágenes IA", "available": "Disponible para nuevos proyectos", "notAvailable": "No disponible en este momento", "blog": "Blog", "copyLink": "<PERSON><PERSON><PERSON> enlace", "minRead": "MIN LECTURA", "articleLinkCopied": "Enlace del artículo copiado al portapapeles", "clickToClose": "Haz clic en cualquier lugar o presiona ESC para cerrar", "promptDetails": "Detalles del prompt", "generateWithPrompt": "Generar con este prompt", "generateWithSettings": "Generar con estas configuraciones", "preset": "<PERSON><PERSON><PERSON><PERSON>", "style": "<PERSON><PERSON><PERSON>", "resolution": "Resolución", "addImage": "<PERSON><PERSON><PERSON>", "modelPreset": "Modelo/Preajuste", "imageDimensions": "Dimensiones de Imagen", "yourImage": "<PERSON>", "generate": "Generar", "nav.aitool": "Herramienta IA", "nav.api": "API", "nav.login": "<PERSON><PERSON><PERSON>", "nav.history": "Historia", "nav.orders": "<PERSON><PERSON><PERSON>", "3D Render": "Renderizado 3D", "Acrylic": "Acrílico", "Anime General": "Anime General", "Creative": "Creativo", "Dynamic": "Dinámico", "Fashion": "Moda", "Game Concept": "<PERSON><PERSON>", "Graphic Design 3D": "Diseño Gráfico 3D", "Illustration": "Ilustración", "None": "<PERSON><PERSON><PERSON>", "Portrait": "Retrato", "Portrait Cinematic": "Retrato Cinematográfico", "Portrait Fashion": "Retrato de Moda", "Ray Traced": "Trazado de Rayos", "Stock Photo": "Foto de Stock", "Watercolor": "<PERSON><PERSON><PERSON><PERSON>", "AI Image Generator": "Generador de Imágenes IA", "Generate AI images from text prompts with a magical particle transformation effect": "Genera imágenes IA a partir de prompts de texto con un efecto mágico de transformación de partículas", "Enter your prompt": "Ingresa tu prompt", "Generating...": "Generando...", "Generate Image": "<PERSON><PERSON>n", "Enter a prompt and click Generate Image to create an AI image": "Ingresa un prompt y haz clic en Generar Imagen para crear una imagen IA", "Prompt:": "Prompt:", "Download": "<PERSON><PERSON><PERSON>", "How It Works": "Cómo Funciona", "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:": "Este generador de imágenes IA usa un efecto de transformación basado en partículas para visualizar el proceso de creación. Cuando ingresas un prompt y haces clic en 'Generar', el sistema:", "Sends your prompt to an AI image generation API": "Envía tu prompt a una API de generación de imágenes IA", "Creates a particle system with thousands of tiny particles": "Crea un sistema de partículas con miles de pequeñas partículas", "Transforms the random noise particles into the generated image": "Transforma las partículas de ruido aleatorio en la imagen generada", "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.": "Las partículas comienzan en un patrón de ruido aleatorio y luego se transforman suavemente en la imagen final, creando un efecto mágico que simula el proceso creativo de la IA.", "Transform": "Transformar", "Transforming...": "Transformando...", "Initializing particles...": "Inicializando partículas...", "Loading image...": "Cargando imagen...", "Creating particle system...": "Creando sistema de partículas...", "Adding event listeners...": "Añadiendo detectores de eventos...", "Ready!": "¡Listo!", "AI Image Particle Effect": "Efecto de Partículas de Imagen IA", "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images": "Una demostración del componente BaseMagicImage que transforma partículas en imágenes generadas por IA", "Click anywhere or press ESC to close": "Haz clic en cualquier lugar o presiona ESC para cerrar", "auth.login": "<PERSON><PERSON><PERSON>", "auth.loginDescription": "Inicia sesión en tu cuenta para continuar", "auth.email": "Correo Electrónico", "auth.enterEmail": "Ingresa tu correo electrónico", "auth.password": "Contraseña", "auth.enterPassword": "Ingresa tu contraseña", "auth.rememberMe": "Recordarme", "auth.welcomeBack": "Bienvenido de vuelta", "auth.signupFailed": "Registro fallido", "auth.signupFailedDescription": "Hubo un error durante el registro. Por favor, inténtalo de nuevo.", "auth.dontHaveAccount": "¿No tienes una cuenta?", "auth.signUp": "Registrarse", "auth.forgotPassword": "¿Olvidaste tu contraseña?", "auth.bySigningIn": "Al iniciar sesi<PERSON>, aceptas nuestros", "auth.termsOfService": "Términos de Servicio", "auth.signUpTitle": "Registrarse", "auth.signUpDescription": "<PERSON>rea una cuenta para comenzar", "auth.name": "Nombre", "auth.enterName": "Ingresa tu nombre", "auth.createAccount": "<PERSON><PERSON><PERSON> cuenta", "auth.alreadyHaveAccount": "¿Ya tienes una cuenta?", "auth.bySigningUp": "Al registrarte, aceptas nuestros", "auth.backToHome": "Volver al inicio", "auth.notVerifyAccount": "Tu cuenta no está verificada. Por favor, verifica tu cuenta para continuar", "auth.verifyAccount": "Verificar cuenta", "auth.resendActivationEmail": "Reenviar correo de activación", "auth.accountRecovery": "Recuperación de Cuenta", "auth.accountRecoveryTitle": "Recupera tu cuenta", "auth.accountRecoveryDescription": "Ingresa tu correo electrónico para recibir instrucciones de restablecimiento de contraseña", "auth.sendRecoveryEmail": "Enviar correo de recuperación", "auth.recoveryEmailSent": "Correo de recuperación enviado", "auth.recoveryEmailSentDescription": "Por favor, revisa tu correo electrónico para instrucciones de restablecimiento de contraseña", "auth.resetPassword": "Restable<PERSON>", "auth.resetPasswordTitle": "Restablece tu contraseña", "auth.resetPasswordDescription": "Ingresa tu nueva contraseña", "auth.newPassword": "Nueva contraseña", "auth.confirmPassword": "Confirmar con<PERSON>", "auth.enterNewPassword": "Ingresa tu nueva contraseña", "auth.enterConfirmPassword": "Confirma tu nueva contraseña", "auth.passwordResetSuccess": "Restablecimiento de contraseña exitoso", "auth.passwordResetSuccessDescription": "Tu contraseña ha sido restablecida exitosamente. Ahora puedes iniciar sesión con tu nueva contraseña", "auth.activateAccount": "Activar Cuenta", "auth.activateAccountTitle": "Activa tu cuenta", "auth.activateAccountDescription": "Tu cuenta está siendo activada...", "auth.accountActivated": "Cuenta activada", "auth.accountActivatedDescription": "Tu cuenta ha sido activada exitosamente. Ahora puedes iniciar sesión", "auth.activationFailed": "Activación fallida", "auth.activationFailedDescription": "Error al activar tu cuenta. Por favor, inténtalo de nuevo o contacta soporte", "auth.backToLogin": "Volver al inicio de sesión", "auth.loginFailed": "Error al iniciar sesión", "auth.loginWithGoogle": "Iniciar se<PERSON><PERSON> con Google", "auth.google": "Google", "auth.filter": "Filter", "validation.invalidEmail": "Correo electrónico inválido", "validation.passwordMinLength": "La contraseña debe tener al menos 8 caracteres", "validation.nameRequired": "El nombre es requerido", "validation.required": "Este campo es requerido", "validation.passwordsDoNotMatch": "Las contraseñas no coinciden", "imageSelect.pleaseSelectImageFile": "Por favor, selecciona un archivo de imagen", "imageSelect.selectedImage": "Imagen se<PERSON>cci<PERSON>", "imageSelect.removeImage": "Eliminar imagen", "pixelReveal.loading": "Cargando imagen...", "pixelReveal.processing": "Procesando imagen...", "pixelReveal.revealComplete": "Revelación de imagen completa", "SIGNIN_WRONG_EMAIL_PASSWORD": "Correo electrónico o contraseña incorrectos", "Try again": "Intentar de nuevo", "aiToolMenu.imagen": "Imagen", "aiToolMenu.videoGen": "Video Gen", "aiToolMenu.speechGen": "Speech Gen", "aiToolMenu.musicGen": "Music Gen", "aiToolMenu.imagen3": "Imagen 3", "aiToolMenu.imagen3Description": "Genera imágenes de alta calidad y detalladas con renderizado de texto preciso para contenido visual creativo.", "aiToolMenu.imagen4": "Imagen 4", "aiToolMenu.imagen4Description": "Expresa tus ideas como nunca antes — con Imagen, la creatividad no tiene límites.", "aiToolMenu.gemini2Flash": "Gemini 2.0 Flash", "aiToolMenu.gemini2FlashDescription": "Gemini 2.0 Flash es una herramienta poderosa para generar imágenes a partir de prompts de texto.", "aiToolMenu.veo2": "Veo 2", "aiToolMenu.veo2Description": "Mayor control, consistencia y creatividad que nunca antes.", "aiToolMenu.veo3": "Veo 3", "aiToolMenu.veo3Description": "Video, conoce audio. Nuestro último modelo de generación de video, diseñado para empoderar a cineastas y narradores.", "aiToolMenu.gemini25Pro": "Gemini 2.5 Pro", "aiToolMenu.gemini25ProDescription": "El modelo de texto a voz más avanzado disponible.", "aiToolMenu.gemini25Flash": "Gemini 2.5 Flash", "aiToolMenu.gemini25FlashDescription": "Procesamiento a gran escala (ej. múltiples pdfs).\nTareas de bajo tiempo de latencia y alto volumen que requieren pensamiento\nCasos de uso agénticos", "aiToolMenu.link": "Enlace", "aiToolMenu.linkDescription": "Usa NuxtLink con superpoderes.", "aiToolMenu.soon": "Pronto", "readArticle": "<PERSON><PERSON>", "switchToLightMode": "Cambiar a modo claro", "switchToDarkMode": "Cambiar a modo oscuro", "profile": "Perfil", "buyCredits.checkout": "Finalizar Compra", "buyCredits.checkoutDescription": "Confirma tu pedido y luego elige tu método de pago.", "buyCredits.orderDetail": "Detalle del pedido", "buyCredits.credits": "C<PERSON>dit<PERSON>", "buyCredits.pricePerUnit": "Precio por unidad", "buyCredits.totalCredits": "Total de créditos", "buyCredits.totalPrice": "Precio total", "buyCredits.payment": "Pago", "buyCredits.submit": "Enviar", "buyCredits.cancel": "<PERSON><PERSON><PERSON>", "pricing.title": "<PERSON><PERSON><PERSON>", "pricing.description": "Elige el plan perfecto para tus necesidades de generación de imágenes", "pricing.comingSoon": "Próximamente", "pricing.comingSoonDescription": "Nuestros planes de precios están siendo finalizados. Vuelve pronto para actualizaciones.", "magicImageDemo.title": "Efecto de Partículas de Imagen IA", "magicImageDemo.description": "Una demostración del componente BaseMagicImage que transforma partículas en imágenes generadas por IA", "magicImageDemo.image": "Imagen", "magicImageDemo.aboutTitle": "Acerca de Este Componente", "magicImageDemo.aboutDescription": "El componente BaseMagicImage usa Three.js para crear un sistema de partículas que puede transformarse entre posiciones aleatorias y una imagen generada por IA. Las partículas se mueven con efectos de remolino y flujo, creando una transformación mágica.", "magicImageDemo.featuresTitle": "Características", "magicImageDemo.features.particleRendering": "Renderizado de imagen basado en partículas", "magicImageDemo.features.smoothTransitions": "Transiciones suaves entre posiciones aleatorias de partículas y formación de imagen", "magicImageDemo.features.interactiveControls": "Controles de cámara interactivos (arrastra para rotar, desplaza para hacer zoom)", "magicImageDemo.features.customizable": "Cantidad de partículas y duración de animación personalizables", "magicImageDemo.features.automatic": "Activación de transformación automática o manual", "magicImageDemo.howItWorksTitle": "Cómo Funciona", "magicImageDemo.howItWorksDescription": "El componente analiza los píxeles de una imagen y crea un sistema de partículas 3D donde cada partícula representa un píxel. Los píxeles más brillantes se posicionan más cerca del espectador, creando un efecto 3D sutil. Las partículas inicialmente se dispersan aleatoriamente en el espacio 3D, luego se animan para formar la imagen cuando se activa.", "privacy.title": "Política de Privacidad", "privacy.description": "Aprende cómo protegemos tu privacidad y manejamos tus datos", "privacy.informationWeCollect": "Información que Recopilamos", "privacy.informationWeCollectDescription": "Recopilamos información que nos proporcionas directamente, como cuando creas una cuenta, usas nuestros servicios o nos contactas para soporte.", "privacy.howWeUseInformation": "Cómo Usamos tu Información", "privacy.howWeUseInformationDescription": "Usamos la información que recopilamos para proporcionar, mantener y mejorar nuestros servicios, procesar transacciones y comunicarnos contigo.", "privacy.informationSharing": "Compartir Información", "privacy.informationSharingDescription": "No vendemos, intercambiamos o transferimos tu información personal a terceros sin tu consentimiento, excepto como se describe en esta política.", "privacy.dataSecurity": "Seguridad de Datos", "privacy.dataSecurityDescription": "Implementamos medidas de seguridad apropiadas para proteger tu información personal contra acceso no autorizado, alteración, divulgación o destrucción.", "privacy.contactUs": "Contáctanos", "privacy.contactUsDescription": "Si tienes alguna pregunta sobre esta Política de Privacidad, por favor contáctanos a través de nuestros canales de soporte.", "terms.title": "Términos de Servicio", "terms.description": "Términos y condiciones para usar los servicios de Imagen", "terms.acceptanceOfTerms": "1. Aceptación de Términos", "terms.acceptanceOfTermsDescription": "Al acceder y usar los servicios de Imagen, aceptas y acuerdas estar sujeto a los términos y disposiciones de este acuerdo.", "terms.useOfService": "2. <PERSON><PERSON> Servicio", "terms.useOfServiceDescription": "Aceptas usar nuestro servicio solo para propósitos legales y de acuerdo con estos Términos de Servicio.", "terms.userAccounts": "3. Cuentas de Usuario", "terms.userAccountsDescription": "Eres responsable de mantener la confidencialidad de tu cuenta y contraseña.", "terms.intellectualProperty": "4. Propiedad Intelectual", "terms.intellectualPropertyDescription": "Todo el contenido y materiales disponibles en nuestro servicio están protegidos por derechos de propiedad intelectual.", "terms.termination": "5. Terminación", "terms.terminationDescription": "Podemos terminar o suspender tu cuenta y acceso al servicio a nuestra sola discreción.", "terms.disclaimers": "6. <PERSON><PERSON><PERSON> de Responsabilidad", "terms.disclaimersDescription": "El servicio se proporciona 'como está' sin garantías de ningún tipo.", "terms.contactUsTerms": "Contáctanos", "terms.contactUsTermsDescription": "Si tienes alguna pregunta sobre estos Términos de Servicio, por favor contáctanos a través de nuestros canales de soporte.", "Describe the video you want to generate...": "Describe el video que deseas generar...", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "appName": "GeminiGen.AI", "quickTopUp": "<PERSON><PERSON><PERSON>", "customTopUp": "Recarga personalizada", "numberOfCredits": "Número de créditos", "paypal": "PayPal", "paypalDescription": "Pague de manera segura con su cuenta de PayPal", "stripe": "Stripe", "stripeDescription": "Pague de manera segura con Stripe", "debitCreditCard": "Tarjeta de débito o crédito", "cardDescription": "Visa, Mastercard, American Express", "payWithCrypto": "Paga con Cripto", "cryptoDescription": "Bitcoin, Ethereum y otras criptomonedas", "profileMenu.guide": "Guía", "profileMenu.logo": "Logotipo", "profileMenu.settings": "Configuración", "profileMenu.components": "Componentes", "loadingMoreItems": "Cargando más elementos...", "promptLabel": "Pronta:", "videoExamples": "Ejemplos de video", "videoExamplesDescription": "Explora estos ejemplos de video con sus indicaciones y configuraciones. Haz clic en cualquier botón de 'Usar este mensaje' para copiar el mensaje a tu campo de entrada.", "useThisPrompt": "Usa este aviso", "model": "<PERSON><PERSON>", "duration": "Duración", "videoTypeSelection": "Seleccionar tipo de video", "notifications.title": "Notifications", "notifications.description": "Your recent notifications and updates", "notifications.totalCount": "{count} notifications", "notifications.markAllRead": "Mark all as read", "notifications.loadMore": "Load more", "notifications.close": "Close", "notifications.empty.title": "No notifications", "notifications.empty.description": "You're all caught up! No new notifications to show.", "notifications.error.title": "Error loading notifications", "notifications.types.default.title": "Notification", "notifications.types.default.description": "You have a new notification", "notifications.types.video_1.title": "Generación de video pendiente", "notifications.types.video_1.description": "La generación de video está esperando ser procesada.", "notifications.types.video_2.title": "Generación de video completada", "notifications.types.video_2.description": "El video se ha generado con éxito.", "notifications.types.video_3.title": "Falló la generación de video", "notifications.types.video_3.description": "La generación de video falló", "notifications.types.image_1.title": "Generación de imagen pendiente", "notifications.types.image_1.description": "La generación de imágenes está esperando ser procesada.", "notifications.types.image_2.title": "Generación de imagen completada", "notifications.types.image_2.description": "La imagen se ha generado con éxito.", "notifications.types.image_3.title": "Fallo en la Generación de Imágenes", "notifications.types.image_3.description": "La generación de imágenes falló", "notifications.types.tts_history_1.title": "Generación de audio pendiente", "notifications.types.tts_history_1.description": "El texto a voz está esperando ser procesado.", "notifications.types.tts_history_2.title": "Generación de audio completada", "notifications.types.tts_history_2.description": "El audio de texto a voz se ha generado con éxito.", "notifications.types.tts_history_3.title": "Error de generación de audio", "notifications.types.tts_history_3.description": "La generación de texto a voz falló", "notifications.types.voice_training_1.title": "Entrenamiento de Voz Pendiente", "notifications.types.voice_training_1.description": "El entrenamiento de voz está esperando para ser procesado.", "notifications.types.voice_training_2.title": "Entrenamiento de Voz Completado", "notifications.types.voice_training_2.description": "El entrenamiento del modelo de voz personalizado se ha completado con éxito.", "notifications.types.voice_training_3.title": "Entrenamiento de voz fallido", "notifications.types.voice_training_3.description": "El entrenamiento de voz falló", "notifications.types.music_1.title": "Generación de Música Pendiente", "notifications.types.music_1.description": "La generación de música está esperando ser procesada.", "notifications.types.music_2.title": "Generación de Música Completa", "notifications.types.music_2.description": "La música de IA se ha generado con éxito.", "notifications.types.music_3.title": "Generación de música fallida", "notifications.types.music_3.description": "La generación de música falló", "notifications.types.speech_1.title": "Generación de discurso pendiente", "notifications.types.speech_1.description": "Su solicitud de generación de discursos está esperando ser procesada.", "notifications.types.speech_2.title": "Generación de discurso completa.", "notifications.types.speech_2.description": "Su discurso ha sido generado exitosamente.", "notifications.types.speech_3.title": "Falló la Generación de Voz", "notifications.types.speech_3.description": "La generación de discurso falló. Por favor, inténtalo de nuevo.", "notifications.time.justNow": "<PERSON><PERSON><PERSON>", "notifications.time.minutesAgo": "Hace {minutes} minutos", "notifications.time.hoursAgo": "Hace {hours} horas", "notifications.time.yesterday": "Ayer", "notifications.status.processing.title": "Procesamiento", "notifications.status.processing.description": "Su solicitud está siendo procesada.", "notifications.status.success.title": "Completado", "notifications.status.success.description": "Completado con éxito", "notifications.status.failed.title": "Fallido", "notifications.status.failed.description": "Ocurrió un error durante el procesamiento.", "notifications.status.warning.title": "Advertencia", "notifications.status.warning.description": "Completado con advertencias", "notifications.status.pending.title": "Pendiente", "notifications.status.pending.description": "<PERSON><PERSON>ando ser procesado", "notifications.status.cancelled.title": "Cancelado", "notifications.status.cancelled.description": "La solicitud fue cancelada.", "footer.nuxtUIOnDiscord": "Nuxt UI on Discord", "profileSettings.emailNotifications": "Email Notifications", "profileSettings.marketingEmails": "Marketing Emails", "profileSettings.securityAlerts": "Security Alerts", "settings": "Configuración", "userMenu.profile": "Perfil", "userMenu.buyCredits": "<PERSON>mp<PERSON>", "userMenu.settings": "Configuración", "userMenu.api": "API", "userMenu.logout": "<PERSON><PERSON><PERSON>", "userMenu.greeting": "<PERSON><PERSON>, {name}", "formats.mp3": "MP3", "formats.wav": "WAV", "channels.mono": "Mono", "channels.stereo": "Estéreo", "options.allow": "<PERSON><PERSON><PERSON>", "options.dontAllow": "No permitir", "options.voices": "Voces", "options.pickVoice": "Elegir voz", "voiceTypes.systemVoices": "Voces del sistema", "voiceTypes.customVoices": "Voces personalizadas", "voiceTypes.premiumVoices": "Voces premium", "voiceTypes.userVoices": "Voces de usuario", "common.home": "<PERSON><PERSON>", "Describe the speech you want to generate...": "Describe el discurso que deseas generar...", "listenToSpeech": "Escuchar discurso", "generateSimilar": "Generar similar", "voice": "Voz", "emotion": "Emoción", "speed": "Velocidad", "speed_settings": "Ajustes de velocidad", "speed_value": "Valor de velocidad", "speed_slider": "Deslizador de velocidad", "apply": "Aplicar", "speech_settings": "Configuración de Voz", "current_speed": "Velocidad actual", "reset_defaults": "Restablecer a valores predeterminados", "outputFormat": "Formato de salida", "outputChannel": "Canal de salida", "selectVoice": "Seleccionar voz", "selectEmotion": "Seleccionar emoción", "selectFormat": "Seleccionar formato", "selectChannel": "Seleccionar canal", "noVoicesAvailable": "No hay voces disponibles", "noEmotionsAvailable": "No hay emociones disponibles", "searchVoices": "Buscar voces...", "searchEmotions": "Buscar emociones...", "noVoicesFound": "No se encontraron voces", "noEmotionsFound": "No se encontraron emociones", "retry": "Reintentar", "noAudioSample": "No hay muestra de audio disponible", "Speech Generation Complete": "Generación de discurso completa", "Your speech has been generated successfully": "Tu discurso ha sido generado con éxito.", "history.tabs.imagen": "Imagen", "history.tabs.video": "Video", "history.tabs.speech": "Discurso", "history.tabs.music": "Música", "history.tabs.history": "Historia", "orders.title": "Historial de pedidos", "orders.description": "Ver su historial de transacciones y pagos", "orders.orderId": "ID de pedido", "orders.amount": "Cantidad", "orders.credits": "C<PERSON>dit<PERSON>", "orders.quantity": "Cantidad", "orders.platform": "Plataforma", "orders.externalId": "ID de transacción", "orders.status.completed": "Completado", "orders.status.success": "Éxito", "orders.status.paid": "<PERSON><PERSON>", "orders.status.pending": "Pendiente", "orders.status.processing": "Procesamiento", "orders.status.failed": "Fallido", "orders.status.cancelled": "Cancelado", "orders.status.error": "Error", "orders.empty.title": "Aún no hay pedidos", "orders.empty.description": "Aún no has hecho ningún pedido. Compra créditos para comenzar a usar nuestros servicios.", "orders.empty.action": "<PERSON>mp<PERSON>", "orders.endOfList": "Has visto todos los pedidos.", "orders.errors.fetchFailed": "No se pudo cargar el historial de pedidos. Por favor, inténtalo de nuevo.", "orders.meta.title": "Historial de pedidos - Imagen AI", "orders.meta.description": "Consulta tu historial de transacciones y pagos en Imagen AI.", "historyPages.imagenDescription": "Navega por tus imágenes y obras de arte generadas por IA.", "historyPages.musicDescription": "Explora tu música y contenido de audio generados por IA.", "historyPages.speechDescription": "Examina tu contenido de voz y discurso generado por IA.", "historyPages.videoDescription": "Explora tus videos y animaciones generados por IA", "historyPages.imagenBreadcrumb": "Imagen", "historyPages.musicBreadcrumb": "Música", "historyPages.speechBreadcrumb": "Discurso", "historyPages.videoBreadcrumb": "Generación de videos", "historyPages.endOfImagesHistory": "Has llegado al final del historial de imágenes.", "historyPages.endOfMusicHistory": "Has llegado al final de la historia de la música.", "historyPages.endOfSpeechHistory": "Has llegado al final del historial de discursos.", "historyPages.endOfVideoHistory": "Has llegado al final del historial de videos.", "historyPages.noVideosFound": "No se encontraron videos", "historyPages.noVideosFoundDescription": "Comienza a generar videos para verlos aquí.", "historyPages.backToLibrary": "Volver a la biblioteca", "historyPages.errorLoadingVideo": "Error al cargar el video", "historyPages.loadingVideoDetails": "Cargando detalles del video...", "historyPages.videoDetails": "Detalles del video", "historyPages.videoInformation": "Información del video", "historyPages.videoNotFound": "El video que buscas no se pudo encontrar o cargar.", "historyPages.aiContentLibraryTitle": "Biblioteca de Contenidos de IA", "historyPages.aiContentLibraryDescription": "Navega y gestiona tu contenido generado por IA en distintas categorías.", "demo.notifications.title": "Tipos y estado de notificaciones de demostración", "demo.notifications.description": "Ejemplos de diferentes tipos de notificaciones con varios estados de estado", "demo.notifications.statusLegend": "Leyenda de estados", "demo.speechVoiceSelect.title": "Selección de Voz de Habla Demostración", "demo.speechVoiceSelect.description": "Demostración del componente reutilizable BaseSpeechVoiceSelectModal con las propiedades modelValue", "aspectRatio": "Relación de aspecto", "Image Reference": "Referencia de imagen", "personGeneration.dontAllow": "Don't Allow", "personGeneration.allowAdult": "Allow Adult", "personGeneration.allowAll": "Allow All", "safety_filter_level": "<PERSON>vel de Filtro de Seguridad", "used_credit": "Crédito utiliza<PERSON>", "Safety Filter": "Filtro de seguridad", "safetyFilter.blockLowAndAbove": "Bloque Bajo y Alto", "safetyFilter.blockMediumAndAbove": "Bloque medio y superior", "safetyFilter.blockOnlyHigh": "<PERSON><PERSON><PERSON><PERSON> solo alto", "safetyFilter.blockNone": "Bloquear Ninguno", "historyFilter.all": "Todo", "historyFilter.imagen": "Imagen", "historyFilter.videoGen": "Video Gen", "historyFilter.speechGen": "Generación de Speech", "Person Generation": "Generación de Personas", "downloadImage": "<PERSON><PERSON><PERSON> imagen", "noImageAvailable": "Imagen no disponible", "enhancePrompt": "Mejorar indicación", "addImages": "Agregar <PERSON>", "generateVideo": "Generar video", "happy": "<PERSON><PERSON><PERSON>", "sad": "Triste", "angry": "<PERSON><PERSON><PERSON>", "excited": "Emocionado", "laughing": "Riendo", "crying": "Llorando", "calm": "Calma", "serious": "Ser<PERSON>", "frustrated": "<PERSON><PERSON><PERSON>", "hopeful": "<PERSON><PERSON><PERSON>do", "narrative": "<PERSON><PERSON><PERSON><PERSON>", "kids' storytelling": "Narración Infantil", "audiobook": "Audiolibro", "poetic": "Poético", "mysterious": "Misterioso", "inspirational": "Inspirador", "surprised": "Sorpresa", "confident": "<PERSON><PERSON><PERSON>", "romantic": "R<PERSON><PERSON><PERSON><PERSON>", "scared": "<PERSON><PERSON><PERSON>", "trailer voice": "<PERSON><PERSON>", "advertising": "Publicidad", "documentary": "Documental", "newsreader": "Presentador de noticias", "weather report": "Informe del tiempo", "game commentary": "Comentario del juego", "interactive": "Interactivo", "customer support": "Atención al Cliente", "playful": "Juguetón", "tired": "Cansado", "sarcastic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disgusted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "whispering": "<PERSON><PERSON><PERSON><PERSON>", "persuasive": "<PERSON><PERSON><PERSON><PERSON>", "nostalgic": "Nostálgico", "meditative": "Meditativo", "announcement": "<PERSON><PERSON><PERSON>", "professional pitch": "Presentación profesional", "casual": "Informal", "exciting trailer": "<PERSON><PERSON><PERSON><PERSON> emocionante", "dramatic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "corporate": "Corporativo", "tech enthusiast": "Entusiasta de la tecnología", "youthful": "Juvenil", "calming reassurance": "Tranquilizante Reafirmación", "heroic": "Heroico", "festive": "Festivo", "urgent": "Urgente", "motivational": "Motivacional", "friendly": "<PERSON><PERSON><PERSON>", "energetic": "Energético", "serene": "<PERSON><PERSON>", "bold": "Negrita", "charming": "Encan<PERSON><PERSON>", "monotone": "Monótono", "questioning": "Cuestionamiento", "directive": "Directiva", "dreamy": "<PERSON><PERSON><PERSON>", "epic": "Épico", "lyrical": "<PERSON><PERSON><PERSON><PERSON>", "mystical": "Místico", "melancholy": "Melancolía", "cheerful": "Alegre", "eerie": "Misterioso", "flirtatious": "<PERSON><PERSON><PERSON>", "thoughtful": "Considerado", "cinematic": "Cinematográfico", "humorous": "Humorístico", "instructional": "Instruccional", "conversational": "Conversacional", "apologetic": "Apologé<PERSON><PERSON>", "excuse-making": "Fabricación de excusas", "encouraging": "Fomentando", "neutral": "Neutral", "authoritative": "Autoritativo", "sarcastic cheerful": "Sarcásticamente Alegre", "reassuring": "Tranquilizador", "formal": "Formal", "anguished": "<PERSON><PERSON><PERSON>", "giggling": "Riéndose", "exaggerated": "Exagerado", "cold": "Frío", "hot-tempered": "Impulsivo", "grateful": "Agradecido", "regretful": "Arrepentido", "provocative": "Provocativo", "triumphant": "<PERSON><PERSON><PERSON><PERSON>", "vengeful": "Vengativo", "heroic narration": "Narración heroica", "villainous": "Villanos", "hypnotic": "Hipnótico", "desperate": "Desesperado", "lamenting": "<PERSON><PERSON><PERSON><PERSON>", "celebratory": "Celebratorio", "teasing": "Bromear", "exhausted": "Agotado", "questioning suspicious": "Cuestionando lo sospechoso", "optimistic": "Optimista", "bright, gentle voice, expressing excitement.": "Voz brillante y suave, expresando emoción.", "low, slow voice, conveying deep emotions.": "Voz baja y lenta, transmitiendo emociones profundas.", "sharp, exaggerated voice, expressing frustration.": "Voz aguda y exagerada, expresando frustración.", "fast, lively voice, full of enthusiasm.": "<PERSON>oz rápida y animada, llena de entusiasmo.", "interrupted, joyful voice, interspersed with laughter.": "Voz interrumpida y alegre, intercalada con risas.", "shaky, low voice, expressing pain.": "<PERSON>oz temblorosa y baja, expresando dolor.", "gentle, steady voice, providing reassurance.": "Voz suave y constante, brindando tranquilidad.", "mature, clear voice, suitable for formal content.": "Voz madura y clara, adecuada para contenido formal.", "weary, slightly irritated voice.": "<PERSON>oz cansada, ligeramente irritada.", "bright voice, conveying positivity and hope.": "<PERSON>oz brillante, transmitiendo positividad y esperanza.", "natural, gentle voice with a slow rhythm.": "Voz natural y suave con un ritmo lento.", "lively, engaging voice, captivating for children.": "Voz animada y cautivadora, atractiva para los niños.", "even, slow voice, emphasizing content meaning.": "Voz pausada y tranquila, enfatizando el significado del contenido.", "rhythmic, emotional voice, conveying subtlety.": "Voz rítmica y emotiva, transmitiendo sutileza.", "low, slow voice, evoking curiosity.": "Voz baja y lenta, que evoca curiosidad.", "strong, passionate voice, driving action.": "Voz fuerte y apasionada, impulsando la acción.", "high, interrupted voice, expressing astonishment.": "Voz alta e interrumpida, expresando asombro.", "firm, powerful voice, persuasive and assuring.": "Voz firme, poderosa, persuasiva y tranquilizadora.", "sweet, gentle voice, suitable for emotional content.": "<PERSON>oz dulce y suave, adecuada para contenido emocional.", "shaky, interrupted voice, conveying anxiety.": "Voz temblorosa e interrumpida, transmitiendo ansiedad.", "deep, strong voice with emphasis, creating suspense.": "Voz profunda y fuerte con énfasis, creando suspense.", "engaging, lively voice, emphasizing product benefits.": "Voz atractiva y animada que resalta los beneficios del producto.", "formal, clear voice with focus on key points.": "<PERSON>oz formal, clara y centrada en puntos clave.", "calm, profound voice, delivering authenticity.": "Voz calmada y profunda que transmite autenticidad.", "standard, neutral voice, clear and precise.": "<PERSON><PERSON>, neutral, clara y precisa.", "bright, neutral voice, suitable for concise updates.": "Voz clara y neutral, adecuada para actualizaciones concisas.", "fast, lively voice, stimulating excitement.": "Voz rápida y animada, estimulando la emoción.", "friendly, approachable voice, encouraging engagement.": "Voz amistosa y accesible, alentando la participación.", "empathetic, gentle voice, easy to connect with.": "Voz empática y suave, fácil de conectar.", "clear voice, emphasizing questions and answers.": "<PERSON>oz clara, enfatizando preguntas y respuestas.", "cheerful, playful voice with a hint of mischief.": "Voz alegre y juguetona con un toque de picardía.", "slow, soft voice lacking energy.": "Voz lenta y suave que carece de energía.", "ironic, sharp voice, sometimes humorous.": "<PERSON><PERSON><PERSON><PERSON>, voz aguda, a veces humorística.", "cold voice, clearly expressing discomfort.": "Voz fría, expresando claramente incomodidad.", "soft, mysterious voice, creating intimacy.": "Voz suave y misteriosa, creando intimidad.", "emotional voice, convincing the listener to act.": "Voz emotiva, convenciendo al oyente a actuar.", "gentle voice, evoking feelings of reminiscence.": "<PERSON>oz suave, evocando sentimientos de reminiscencia.", "even, relaxing voice, suitable for mindfulness.": "Voz suave y relajante, adecuada para la atención plena.", "clear voice, emphasizing key words.": "<PERSON><PERSON> clara, enfatizando palabras clave.", "confident, clear voice, ideal for business presentations.": "<PERSON>oz segura y clara, ideal para presentaciones empresariales.", "natural, friendly voice, as if talking to a friend.": "Voz natural y amigable, como si estuvieras hablando con un amigo.", "fast, powerful voice, creating tension and excitement.": "<PERSON><PERSON><PERSON><PERSON>, potente voz, creando tensión y emoción.", "emphasized, suspenseful voice, creating intensity.": "Énfasis, voz suspensiva, creando intensidad.", "professional, formal voice, suitable for business content.": "Voz profesional y formal, adecuada para contenido empresarial.", "energetic, lively voice, introducing new technologies.": "Voz enérgica y animada, introduciendo nuevas tecnologías.", "vibrant, cheerful voice, appealing to younger audiences.": "Voz vibrante y alegre, atractiva para el público más joven.", "gentle, empathetic voice, easing concerns.": "Voz suave y empática, aliviando preocupaciones.", "strong, decisive voice, full of inspiration.": "<PERSON>oz fuerte y decidida, llena de inspiración.", "bright, excited voice, suitable for celebrations.": "Voz brillante y entusiasta, adecuada para celebraciones.", "fast, strong voice, emphasizing urgency.": "<PERSON>oz rápida y fuerte, enfatizando la urgencia.", "passionate, inspiring voice, encouraging action.": "Voz apasionada e inspiradora, alentando a la acción.", "warm, approachable voice, fostering connection.": "Voz cálida y accesible, que fomenta la conexión.", "fast, powerful voice, brimming with enthusiasm.": "Voz rápida y poderosa, rebosante de entusiasmo.", "slow, gentle voice, evoking peace and tranquility.": "Voz lenta y suave, que evoca paz y tranquilidad.", "firm, assertive voice, exuding confidence.": "Voz firme y asertiva, irradiando confianza.", "warm, captivating voice, leaving a strong impression.": "Voz cálida y cautivadora, que deja una fuerte impresión.", "flat, unvaried voice, conveying neutrality or irony.": "Voz plana, invariable, que transmite neutralidad o ironía.", "curious voice, emphasizing questions.": "Voz curiosa, enfatizando preguntas.", "firm, clear voice, guiding the listener step-by-step.": "Voz firme y clara, guiando al oyente paso a paso.", "gentle, slow voice, evoking a floating sensation.": "Voz suave y pausada, evocando una sensación de flotación.", "deep, resonant voice, emphasizing grandeur.": "Voz profunda y resonante, que enfatiza la grandeza.", "soft, melodic voice, similar to singing.": "Voz suave y melódica, similar al canto.", "low, drawn-out voice, evoking mystery.": "<PERSON>oz baja y prolongada, evocando misterio.", "slow, low voice, conveying deep sadness.": "<PERSON>oz lenta y baja, transmitiendo profunda tristeza.", "bright, energetic voice, full of positivity.": "<PERSON>oz brillante y enérgica, llena de positividad.", "low, whispery voice, evoking fear or strangeness.": "<PERSON>oz baja y susurrante, que evoca miedo o extrañeza.", "sweet, teasing voice, full of allure.": "<PERSON>oz dulce y seductora, llena de atractivo.", "slow, reflective voice, full of contemplation.": "<PERSON>oz lenta, reflexiva, llena de contemplación.", "resonant, emphasized voice, creating a movie-like effect.": "Voz resonante y enfatizada, creando un efecto cinematográfico.", "lighthearted, cheerful voice, sometimes exaggerated.": "Voz alegre y jovial, a veces exagerada.", "clear, slow voice, guiding the listener step-by-step.": "Voz clara y pausada, guiando al oyente paso a paso.", "natural voice, as if chatting with the listener.": "Voz natural, como si estuvieras charlando con el oyente.", "soft, sincere voice, expressing regret.": "Voz suave y sincera, expresando arrepentimiento.", "hesitant, uncertain voice, sometimes awkward.": "<PERSON>oz vacilante, incierta, a veces torpe.", "warm voice, providing motivation and support.": "<PERSON>oz cálida, que ofrece motivación y apoyo.", "even voice, free of emotional bias.": "Voz uniforme, libre de sesgo emocional.", "strong, powerful voice, exuding credibility.": "Voz fuerte y poderosa, que irradia credibilidad.", "cheerful voice with an undertone of mockery.": "Voz alegre con un tono de burla.", "gentle, empathetic voice, providing comfort.": "Voz suave y empática, brindando consuelo.", "clear, polite voice, suited for formal occasions.": "Voz clara y cortés, adecuada para ocasiones formales.", "urgent, shaky voice, expressing distress.": "<PERSON><PERSON><PERSON>, voz temblorosa, expresando angustia.", "interrupted voice, mixed with light laughter.": "<PERSON>oz interrumpida, mezclada con una risa ligera.", "loud, emphasized voice, often humorous.": "Voz alta y enfatizada, a menudo humorística.", "flat, unemotional voice, conveying detachment.": "Voz plana, sin emociones, que transmite desapego.", "fast, sharp voice, sometimes out of control.": "<PERSON>oz rá<PERSON>a, agu<PERSON>, a veces fuera de control.", "warm, sincere voice, expressing appreciation.": "<PERSON>oz cálida y sincera, expresando aprecio.", "low, subdued voice, full of remorse.": "<PERSON>oz baja y apagada, llena de remordimiento.", "challenging, strong voice, full of insinuation.": "<PERSON><PERSON><PERSON><PERSON>, voz fuerte, llena de insinuación.", "loud, powerful voice, full of victory.": "<PERSON>oz fuerte y poderosa, llena de victoria.", "low, cold voice, expressing determination for revenge.": "Voz baja y fría, expresando determinación por venganza.", "strong, inspiring voice, emphasizing heroic deeds.": "Voz fuerte e inspiradora, enfatizando hazaña<PERSON> heroicas.", "low, drawn-out voice, full of scheming.": "<PERSON>oz baja y prolongada, llena de intriga.", "even, repetitive voice, drawing the listener in.": "Voz uniforme y repetitiva, atrayendo al oyente.", "urgent, shaky voice, expressing hopelessness.": "<PERSON><PERSON><PERSON>, voz temblorosa, expresando desesperanza.", "low, sorrowful voice, as if mourning.": "Voz baja y triste, como si estuviera de luto.", "excited, joyful voice, full of festive spirit.": "Voz emocionada y alegre, llena de espíritu festivo.", "light, playful voice, sometimes mockingly.": "<PERSON>oz ligera y juguetona, a veces burlona.", "weak, broken voice, expressing extreme fatigue.": "Voz débil y rota, expresando fatiga extrema.", "slow, emphasized voice, full of suspicion.": "<PERSON>oz lenta y enfatizada, llena de sospecha.", "bright, hopeful voice, creating positivity.": "Voz brillante y esperanzadora, creando positividad.", "Your audio will be processed with the latest stable model.": "Su audio será procesado con el modelo estable más reciente.", "Your audio will be processed with the latest beta model.": "Tu audio será procesado con el último modelo beta.", "You don't have any saved prompts yet.": "Todavía no tienes ningún mensaje guardado.", "Commercial Use": "Uso comercial", "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes. However, you may not resell, redistribute, or sublicense the speech output without prior written consent from Text To Speech OpenAI.": "Tienes el derecho de utilizar la salida de voz generada por nuestros servicios para propósitos personales, educativos o comerciales. Sin embargo, no puedes revender, redistribuir o sublicenciar la salida de voz sin el consentimiento previo por escrito de Text To Speech OpenAI.", "Other people’s privacy": "La privacidad de otras personas", "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.": "Debes respetar la privacidad de los demás al utilizar nuestros servicios. No subas ni crees productos de voz que contengan información personal, datos confidenciales o material protegido por derechos de autor sin permiso.", "{price}$ per credit": "{price}$ por crédito", "Pricing": "<PERSON><PERSON><PERSON>", "Simple and flexible. Only pay for what you use.": "Simple y flexible. Solo paga por lo que usas.", "Pay as you go": "Paga según el consumo", "Flexible": "Flexible", "Input characters": "Caracteres de entrada", "Audio model": "Modelo de audio", "Credits": "C<PERSON>dit<PERSON>", "Cost": "Costo", "HD quality voices": "Voces de calidad HD", "Advanced model": "<PERSON><PERSON>", "Buy now": "Compra ahora", "Paste your text to calculate": "Pegue su texto para calcular", "Paste your text here...": "Pega tu texto aquí...", "Calculate": "Calcular", "Estimate your cost by drag the slider below or": "Estime su costo arrastrando el control deslizante a continuación o", "calming": "Tranquilizante", "customer": "Cliente", "exciting": "emocionante", "excuse": "<PERSON><PERSON><PERSON>", "game": "Ju<PERSON>", "hot": "Caliente", "kids": "<PERSON><PERSON><PERSON>", "professional": "Profesional", "tech": "Tecnología", "trailer": "Remolque", "weather": "Clima", "No thumbnail available": "Miniatura no disponible", "Debit or Credit Card": "Tarjeta de débito o crédito", "Visa, Mastercard, American Express and more": "Visa, Mastercard, American Express y más", "Top up now": "<PERSON><PERSON><PERSON> ahora", "noVideoAvailable": "No hay video disponible", "common.back": "Atrás", "common.edit": "<PERSON><PERSON>", "common.save": "Guardar", "common.cancel": "<PERSON><PERSON><PERSON>", "common.delete": "Eliminar", "common.copy": "Copiar", "common.copied": "Copiado", "common.manage": "Gestionar", "aiToolMenu.textToImage": "Texto a imagen", "profileMenu.integration": "Integración", "videoTypes.examples.tikTokDanceTrend": "Tendencia de baile en TikTok", "videoTypes.examples.energeticDanceDescription": "Video de baile enérgico con colores vibrantes, cortes rápidos, música de moda, formato vertical, estilo de redes sociales.", "videoTypes.examples.instagramReel": "<PERSON><PERSON> de Instagram", "videoTypes.examples.lifestyleDescription": "Contenido de estilo de vida con visuales estéticos, transiciones suaves, efectos de moda, narración atractiva.", "videoTypes.examples.comedySketch": "Espectá<PERSON>lo de Comedia", "videoTypes.examples.funnyComedyDescription": "Escena cómica divertida con personajes expresivos, situaciones humorísticas, diálogos entretenidos, ambiente desenfadado", "videoTypes.examples.productLaunchAd": "Anuncio de Lanzamiento de Producto", "videoTypes.examples.professionalCorporateDescription": "Video corporativo profesional con presentación ejecutiva, entorno de oficina limpio, estilo formal de negocios.", "videoTypes.examples.quickPromoVideo": "Vídeo Promocional Rápido", "videoTypes.examples.fastPacedPromoDescription": "Contenido promocional de ritmo rápido con producción eficiente, visuales rentables, mensajes simplificados", "videoTypes.examples.birthdayGreeting": "Saludo de cumpleaños", "videoTypes.examples.personalizedBirthdayDescription": "Video de cumpleaños personalizado con decoraciones festivas, iluminación cálida, ambiente de celebración, mensaje sincero.", "videoTypes.examples.brandStoryVideo": "Video de la historia de la marca", "videoTypes.examples.tutorialVideo": "Video tutorial", "videoTypes.examples.manOnThePhone": "Hombre al teléfono", "videoTypes.examples.runningSnowLeopard": "<PERSON><PERSON><PERSON> de las nieves corriendo", "videoTypes.examples.snowLeopard": "<PERSON><PERSON><PERSON> de las nieves", "videoTypes.styles.cartoonAnimated": "Video de estilo animado o de dibujos animados", "videoTypes.styles.naturalDocumentary": "Metraje de estilo documental natural", "videoTypes.styles.naturalLifelike": "Estilo de video natural y realista", "videoTypes.styles.professionalMovieQuality": "Calidad profesional como de película con iluminación dramática", "videoTypes.styles.creativeStylized": "Efectos de video creativos y estilizados", "videoTypes.styles.retroVintage": "Estética de video retro o vintage", "historyFilter.dialogueGen": "Generación de Diálogos", "historyFilter.speechGenDocument": "Generación de Discurso a partir de Documento", "demo.notifications.availableNotificationTypes": "Tipos de notificación disponibles", "demo.speechVoiceSelect.example1": "Ejemplo 1: <PERSON><PERSON>determinado", "demo.speechVoiceSelect.example2": "Ejemplo 2: <PERSON><PERSON><PERSON> peque<PERSON>", "demo.speechVoiceSelect.example3": "Ejemplo 3: <PERSON><PERSON><PERSON> grande", "demo.speechVoiceSelect.example4": "Ejemplo 4: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> errores", "demo.speechVoiceSelect.example5": "Ejemplo 5: Comparación de estado", "demo.speechVoiceSelect.mainNarrator": "Narrador principal", "demo.speechVoiceSelect.characterVoice": "Voz del personaje", "demo.speechVoiceSelect.selectedVoicesSummary": "Resumen de Voces Seleccionadas:", "demo.speechVoiceSelect.clearAll": "<PERSON><PERSON><PERSON> todo", "demo.speechVoiceSelect.setRandomVoices": "Configurar voces aleatorias", "demo.speechVoiceSelect.logToConsole": "Registrar en la consola", "demo.speechVoiceSelect.notSelected": "No seleccionado", "demo.speechVoiceSelect.voiceSelectionsChanged": "Selección de voz cambiada", "demo.historyWrapper.title": "Demostración de la insignia de estado de HistoryWrapper", "demo.historyWrapper.normalStatus": "Ejemplo 1: Estado Normal (estado = 1)", "demo.historyWrapper.processingStatus": "Ejemplo 2: Estado de procesamiento (estado = 2)", "demo.historyWrapper.errorStatus": "Ejemplo 3: <PERSON><PERSON><PERSON> Error (estado = 3) - Muestra Insignia de Error", "demo.historyWrapper.multipleErrorExamples": "Ejemplo 4: <PERSON><PERSON><PERSON><PERSON> ejemplos de error", "demo.historyWrapper.statusComparison": "Ejemplo 5: Comparación de estados", "demo.historyWrapper.normalImageGeneration": "Generación de imágenes normalizadas", "demo.historyWrapper.videoGenerationInProgress": "Generación de video en progreso", "demo.historyWrapper.speechGenerationFailed": "Generación de Discurso Fallida", "demo.historyWrapper.imageFailed": "<PERSON>n fallida", "demo.historyWrapper.videoFailed": "Video fallido", "demo.historyWrapper.speechFailed": "El discurso falló", "demo.historyWrapper.statusSuccess": "Estado: Éxito", "demo.historyWrapper.statusProcessing": "Estado: Procesando", "demo.historyWrapper.statusError": "Estado: <PERSON><PERSON><PERSON>", "demo.historyWrapper.status1Success": "Estado 1: Éxito", "demo.historyWrapper.status2Processing": "Estado 2: Procesando", "demo.historyWrapper.badgeBehavior": "Comportamiento de Insignia:", "demo.historyWrapper.showsOnlyTypeAndStyle": "Muestra solo insignias de tipo y estilo", "demo.historyWrapper.showsTypeStyleAndError": "Muestra tipo, estilo y distintivo de error rojo con icono de alerta", "demo.historyWrapper.redBackgroundWithWhite": "Fondo rojo con texto blanco y icono de círculo de alerta", "demo.historyWrapper.allBadgesHideOnHover": "Todos los distintivos se ocultan al pasar el ratón para mostrar el contenido superpuesto.", "demo.speechVoiceCaching.title": "Prueba de Cacheo de Voz de Discurso", "demo.speechVoiceCaching.description": "Prueba para verificar la caché de voces entre diferentes componentes.", "demo.speechVoiceCaching.component1Modal": "Componente 1 - Modal", "demo.speechVoiceCaching.component3RegularSelect": "Componente 3 - Selección Regular", "demo.speechVoiceCaching.forceReloadVoices": "Forzar la Recarga de Voces", "demo.speechVoiceCaching.clearAllSelections": "Borrar todas las selecciones", "demo.speechVoiceCaching.logStoreState": "Estado del almacén de registros", "demo.speechVoiceCaching.refreshPageInstructions": "Actualiza la página y abre cualquier componente - se cargará de nuevo", "demo.speechVoiceCaching.checkNetworkTab": "Revisa la pestaña de Red para confirmar las llamadas de API.", "demo.speechVoiceCaching.selectedVoicePersist": "La voz seleccionada se guardará en localStorage.", "demo.speechVoiceCaching.pageMounted": "<PERSON><PERSON><PERSON><PERSON> montada, la tienda se inicializará automáticamente si es necesario.", "integration.title": "Integración", "integration.subtitle": "Administra tus claves API y la configuración de integración", "integration.apiKeys": "Claves de API", "integration.apiKeysDescription": "Administra tus claves API para acceso programático.", "integration.webhook": "Webhook", "integration.webhookDescription": "Configurar la URL del webhook para notificaciones", "apiKeys.title": "Claves API", "apiKeys.subtitle": "Administra tus claves API para acceso programático", "apiKeys.create": "<PERSON><PERSON>r clave de <PERSON>", "apiKeys.createNew": "Crear nueva clave API", "apiKeys.createFirst": "Crear la primera clave API", "apiKeys.name": "Nombre", "apiKeys.nameDescription": "<PERSON> a tu clave API un nombre descriptivo.", "apiKeys.namePlaceholder": "p. ej., Mi Clave API de la Aplicación", "apiKeys.nameRequired": "El nombre de la clave API es obligatorio.", "apiKeys.createdAt": "<PERSON><PERSON><PERSON>", "apiKeys.noKeys": "Sin claves API", "apiKeys.noKeysDescription": "Crea tu primera clave de API para comenzar con el acceso programático.", "apiKeys.created": "Clave API creada con éxito", "apiKeys.createError": "Error al crear la clave API", "apiKeys.deleted": "La clave API se eliminó correctamente.", "apiKeys.deleteError": "No se pudo eliminar la clave de API", "apiKeys.deleteConfirm": "Eliminar clave API", "apiKeys.deleteWarning": "¿Está seguro de que desea eliminar esta clave API? Esta acción no se puede deshacer.", "apiKeys.copied": "Clave API copiada al portapapeles", "apiKeys.copyError": "Error al copiar la clave API", "webhook.title": "Configuración de Webhook", "webhook.subtitle": "Configura la URL del webhook para notificaciones en tiempo real", "webhook.configuration": "URL del webhook", "webhook.currentUrl": "URL actual del webhook", "webhook.currentUrlDescription": "Esta URL recibirá solicitudes POST para eventos de webhook.", "webhook.notConfigured": "No se ha configurado la URL del webhook", "webhook.url": "URL del webhook", "webhook.urlDescription": "Ingrese la URL donde desea recibir notificaciones del webhook", "webhook.urlPlaceholder": "https://tu-dominio.com/webhook", "webhook.urlRequired": "Por favor, ingrese una URL de webhook primero.", "webhook.invalidUrl": "Por favor, introduce una URL válida.", "webhook.saved": "URL de webhook guardado exitosamente", "webhook.saveError": "Error al guardar la URL del webhook", "webhook.test": "Prueba", "webhook.testSent": "Prueba enviada", "webhook.testDescription": "El webhook de prueba se envió correctamente", "webhook.information": "Información del Webhook", "webhook.howItWorks": "Cómo funciona", "webhook.description": "<PERSON>uando esté configurado, enviaremos solicitudes HTTP POST a tu URL de webhook siempre que ocurran ciertos eventos en tu cuenta.", "webhook.events": "Eventos de Webhook", "webhook.imageGenerated": "Generación de imagen completada", "webhook.imageGenerationFailed": "La generación de imágenes falló", "webhook.creditUpdated": "Saldo de crédito actualizado", "webhook.payloadFormat": "Formato de carga útil", "webhook.payloadDescription": "Las solicitudes de webhook se enviarán como JSON con la siguiente estructura:", "webhook.security": "Seguridad", "webhook.securityDescription": "Recomendamos utilizar URLs HTTPS e implementar la verificación de firmas para garantizar la autenticidad del webhook.", "error.general": "Error", "error.validation": "Error de validación", "error.required": "Campo obligatorio", "success.saved": "Guardado con éxito", "success.created": "Creado con éxito", "success.deleted": "Eliminado con éxito", "success.copied": "Copiado al portapapeles", "confirmDelete": "Confirmar eliminación", "confirmDeleteDescription": "¿Estás seguro de que deseas eliminar este elemento? Esta acción no se puede deshacer.", "historyDeleted": "Elemento de historial eliminado con éxito.", "deleteError": "No se pudo eliminar el elemento del historial.", "Regenerate Image": "Regenerar imagen", "You haven't made any changes to the settings. Are you sure you want to regenerate the same image?": "No has realizado ningún cambio en la configuración. ¿Estás seguro de que deseas regenerar la misma imagen?", "Yes, Regenerate": "Sí, regenerar", "Cancel": "<PERSON><PERSON><PERSON>", "models.imagen4Fast": "Imagen 4 Rápida", "models.imagen4Ultra": "Imagen 4 Ultra", "voiceTypes.favoriteVoices": "Voces Favoritas", "voiceTypes.geminiVoices": "Voces de Géminis", "speech.dialogueGeneration.complete": "Generación de diálogo completada", "speech.dialogueGeneration.failed": "Generación de diálogo fallida", "speech.dialogueGeneration.pending": "Generación de Diálogo Pendiente", "speech.dialogueGeneration.dialogueGen": "Generador de Diálogos", "speech.dialogueGeneration.successMessage": "Tu diálogo se ha generado con éxito.", "speech.speechGeneration.complete": "Generación de voz completa", "speech.speechGeneration.failed": "Falló la generación de voz", "speech.speechGeneration.pending": "Generación de Discurso Pendiente", "speech.speechGeneration.successMessage": "Su discurso se ha generado exitosamente.", "speech.speechGeneration.requestWaiting": "Su solicitud de generación de discurso está en espera de ser procesada.", "speech.errors.failedToLoadEmotions": "No se pudo cargar las emociones", "tts-document": "Archivo a Voz", "assignVoicesToSpeakers": "Asignar voces a los hablantes", "speakers": "Altavoces", "addSpeaker": "Agregar orador", "noVoiceAssigned": "No hay voz asignada", "noSpeakersAdded": "Aún no se han a<PERSON><PERSON><PERSON> al<PERSON>.", "assignVoiceToSpeaker": "<PERSON><PERSON><PERSON> voz a {speaker}", "assigned": "<PERSON><PERSON><PERSON>", "assign": "<PERSON><PERSON><PERSON>", "editSpeaker": "<PERSON><PERSON>", "speakerName": "Nombre del orador", "enterSpeakerName": "Ingresar nombre del orador", "save": "Guardar", "speaker": "Altavoz", "assignVoices": "<PERSON><PERSON><PERSON>", "speakersWithVoices": "\"{assigned}/{total} hablantes tienen voces\"", "dialogs": "Diálogos", "addDialog": "<PERSON><PERSON><PERSON>", "enterDialogText": "Ingrese el texto del diálogo...", "selectSpeaker": "Se<PERSON><PERSON><PERSON><PERSON> or<PERSON>", "generateDialogSpeech": "Generar diálogo hablado", "voice 1": "Voz 1", "voice 2": "Voz 2", "uuid": "UUID", "output_format": "Formato de salida", "output_channel": "Canal de salida", "file_name": "Nombre del archivo", "file_size": "Tamaño de archivo", "speakers_count": "Recuento de oradores", "custom_prompt": "Instrucción personalizada", "Please wait a moment...": "Por favor, espere un momento...", "Click to copy": "<PERSON>cer clic para copiar", "Copied to clipboard": "Copiado al portapapeles", "UUID has been copied to clipboard": "El UUID se ha copiado al portapapeles.", "Credits: {credits} remaining": "Créditos: {credits} restantes", "This generation will cost: {cost} Credits": "Esta generación costará: {cost} Créditos", "Your generated video will appear here": "Tu video generado aparecerá aquí.", "Regenerate Video": "Regenerar video", "You haven't made any changes to the settings. Are you sure you want to regenerate the same video?": "No has hecho ningún cambio en la configuración. ¿Estás seguro de que quieres regenerar el mismo video?", "Your generated speech will appear here": "Tu discurso generado aparecerá aquí.", "Regenerate Speech": "Regenerar discurso", "You haven't made any changes to the settings. Are you sure you want to regenerate the same speech?": "No has hecho ningún cambio en la configuración. ¿Estás seguro de que quieres regenerar el mismo discurso?", "Generated Speech": "Discurso generado", "Generating speech...": "Generando discurso...", "View Details": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Speech Examples": "Ejemplos de discursos", "Click on any example to use its prompt for speech generation": "Haz clic en cualquier ejemplo para utilizar su indicación para la generación de voz.", "Click to use": "Haz clic para usar", "videoStyles.selectVideoStyle": "Seleccionar estilo de video", "videoStyles.cinematic": "Cinematográfico", "videoStyles.realistic": "Realista", "videoStyles.animated": "Animado", "videoStyles.artistic": "Artístico", "videoStyles.documentary": "Documental", "videoStyles.vintage": "Vintage", "ui.buttons.downloadApp": "Descargar aplicación", "ui.buttons.signUp": "Registrarse", "ui.buttons.viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "ui.buttons.seeLater": "Nos vemos más tarde", "ui.buttons.selectFile": "Seleccionar archivo", "ui.buttons.selectFiles": "Seleccionar archivos", "ui.buttons.pickAVoice": "Elige una voz", "ui.buttons.topUpNow": "<PERSON><PERSON><PERSON> ahora", "ui.buttons.pressEscToClose": "Presiona ESC para cerrar", "ui.labels.clickToCopy": "<PERSON>cer clic para copiar", "ui.labels.copiedToClipboard": "Copiado al portapapeles", "ui.labels.noAudioAvailable": "No hay audio disponible", "ui.labels.noThumbnailAvailable": "Miniatura no disponible", "ui.labels.noPromptAvailable": "No hay mensaje disponible.", "ui.labels.videoModel": "Modelo de Video", "ui.labels.speechModel": "<PERSON><PERSON> de voz", "ui.labels.generatedSpeech": "Discurs<PERSON>", "ui.labels.defaultVoice": "<PERSON><PERSON> predeterminada", "ui.labels.selectAnyVoice": "Selecciona cualquier voz", "ui.labels.cameraMotion": "Movimiento de cámara", "ui.labels.transform": "Transformar", "ui.labels.transforming": "Transformando...", "ui.messages.imageLoaded": "Imagen cargada", "ui.messages.imageRevealComplete": "Revelación de imagen completa", "ui.messages.processingImage": "Procesando imagen", "ui.messages.videoLoaded": "Video cargado", "ui.messages.videoProcessing": "Procesamiento de video", "ui.messages.invalidDownloadLink": "Enlace de descarga no válido", "ui.messages.pleaseSelectSupportedFile": "Por favor, selecciona un archivo compatible", "ui.messages.deleteConfirm": "Confirmar eliminación", "ui.messages.deleteFailed": "Eliminación fallida", "ui.messages.youHaveNewNotification": "Tienes una nueva notificación", "ui.messages.yourGenerationIsReady": "Tu generación está lista", "ui.errors.errorLoadingImage": "Error al cargar la imagen:", "ui.errors.failedToCopy": "Error al copiar: ", "ui.errors.failedToPlayAudioPreview": "No se pudo reproducir la vista previa de audio:", "ui.errors.wavesurferError": "<PERSON><PERSON><PERSON>:", "ui.errors.somethingWentWrong": "Algo salió mal. <PERSON><PERSON> favor, inténtalo de nuevo.", "ui.errors.supabaseUrlRequired": "Se requieren la URL de Supabase y la clave anónima.", "dialog.startTypingHere": "Empieza a escribir el diálogo aquí...", "payment.debitCreditCard": "Tarjeta de débito o crédito", "payment.cardDescription": "Visa, Mastercard, American Express y más", "Style Description": "Descripción del estilo", "Dialog Content": "Contenido del diálogo", "Your generated dialog will appear here": "Tu diálogo generado aparecerá aquí.", "Regenerate Dialog": "Regenerar diálogo", "Generated Dialog": "Diálogo Generado", "Generating dialog...": "Generando diálogo...", "Dialog Information": "Información del diálogo", "Audio Player": "Reproductor de audio", "Voices": "Voces", "Voice 1": "Voz 1", "Voice 2": "Voz 2", "Dialog Examples": "Ejemplos de diálogo", "Click on any example to use its style or dialog content": "Haz clic en cualquier ejemplo para usar su estilo o contenido de diálogo.", "Use Style": "<PERSON>ar estilo", "Use Dialog": "Usar diálogo", "personGeneration": "Generación de Personas", "Imagen": "Imagen", "On": "En", "Off": "<PERSON><PERSON><PERSON>", "Prompts will always be refined to improve output quality": "Los avisos siempre se perfeccionarán para mejorar la calidad del resultado.", "Prompts will not be modified": "Los indicaciones no serán modificadas", "Tips": "Consejos", "Your video is still being generated in the background. You can close this page and check the history tab for the generated video and we will notify you when it is ready.": "Tu video todavía se está generando en segundo plano. Puedes cerrar esta página y comprobar la pestaña de historial para ver el video generado y te notificaremos cuando esté listo.", "Go to History": "<PERSON><PERSON> a <PERSON>", "footer.youtube": "Youtube", "footer.doctransGPT": "DoctransGPT", "footer.textToSpeechOpenAI": "Texto a Voz OpenAI", "footer.privacyPolicy": "Política de privacidad", "footer.termsOfService": "Términos de servicio", "footer.terms": "Térm<PERSON>s", "footer.privacy": "Privacidad", "Generate": "Generar", "Prompt": "Solicitud", "Generate Video": "Generar video", "ui.errors.generationFailed": "Generación fallida", "downloadVideo": "Des<PERSON><PERSON> video", "imageStyles.selectImageStyle": "Seleccionar estilo de imagen", "imageStyles.none.description": "No se aplicó un estilo específico", "imageStyles.3d-render.description": "Renderizar imagen en 3D", "imageStyles.acrylic.description": "<PERSON><PERSON>r imagen con estilo de pintura acrílica", "imageStyles.anime-general.description": "Generar imagen en estilo anime", "imageStyles.creative.description": "Aplicar efectos artísticos creativos", "imageStyles.dynamic.description": "Crear visuales dinámicos y enérgicos", "imageStyles.fashion.description": "Imagen de estilo para fotografía de moda", "imageStyles.game-concept.description": "Diseño de imagen para arte conceptual de videojuegos", "imageStyles.graphic-design-3d.description": "Aplicar elementos de diseño gráfico en 3D", "imageStyles.illustration.description": "Crear ilustraciones artísticas", "imageStyles.portrait.description": "Optimizar para fotografía de retrato", "imageStyles.portrait-cinematic.description": "<PERSON><PERSON>r estilo de retrato cinematográfico", "imageStyles.portrait-fashion.description": "Aplica un estilo de retrato de moda", "imageStyles.ray-traced.description": "Renderizar con efectos de trazado de rayos", "imageStyles.stock-photo.description": "Crear estilo de foto de stock profesional", "imageStyles.watercolor.description": "Aplicar efectos de pintura en acuarela", "imageStyles.examples": "<PERSON><PERSON><PERSON><PERSON>", "ui.messages.dragDropOrClick": "Arrastra y suelta los archivos aquí o haz clic para seleccionar.", "ui.messages.dropFilesHere": "Suelta archivos aquí", "ui.messages.selectMultipleFiles": "Puede seleccionar varios archivos", "ui.messages.selectSingleFile": "Selecciona un archivo para subir", "ui.messages.supportedFormats": "Formatos compatibles", "ui.messages.releaseToUpload": "Liberar para subir", "ui.labels.generatedAudio": "Audio generado", "ui.actions.showResult": "Mostrar resultado", "ui.actions.hideResult": "Ocultar resultado", "ui.messages.speechGenerating": "Generando habla...", "Your speech is still being generated in the background. You can close this page and check the history tab for the generated audio and we will notify you when it is ready.": "Tu discurso todavía se está generando en segundo plano. Puedes cerrar esta página y consultar la pestaña de historial para el audio generado y te notificaremos cuando esté listo.", "downloadAudio": "Descargar audio"}