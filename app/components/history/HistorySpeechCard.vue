<script setup lang="ts">
import { ref, computed } from 'vue'
import WaveformPlayer from '../WaveformPlayer.vue'

const router = useRouter()
const historyStore = useHistoryStore()
const { showDetailModal, historyDetail } = storeToRefs(historyStore)

interface Props {
  data?: any
  audioUrl?: string
  title?: string
  prompt?: string
  preset?: string
  voice?: string
  duration?: string
  orientation?: string
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}),
  audioUrl: '',
  title: '',
  prompt: '',
  preset: '',
  voice: '',
  duration: '',
  orientation: 'vertical',
  loading: false
})

const lastAudio = computed(() => {
  return (
    props.data?.generated_audio?.[props.data?.generated_audio?.length - 1] || ''
  )
})

const audioUrl = computed(() => {
  // Use individual prop first, then fall back to data prop
  return props.audioUrl || lastAudio.value?.audio_url || ''
})

const title = computed(() => {
  // Use individual prop first, then fall back to data prop
  return (
    props.title
    || props.data?.input_text
    || props.data?.name
    || 'Generated Speech'
  )
})

const prompt = computed(() => {
  // Use individual prop first, then fall back to data prop
  return (
    props.prompt
    || props.data?.input_text
    || props.data?.custom_prompt
    || 'No prompt available'
  )
})

const model = computed(() => {
  // Use individual prop first, then fall back to data prop
  return (
    props.preset
    || props.data?.model_name
    || props.data?.model
    || 'Speech Model'
  )
})

const voice = computed(() => {
  // Use individual prop first, then fall back to data prop
  return props.voice || props.data?.voice || 'Default Voice'
})

const isFullScreenOpen = ref(false)
const showResult = ref(false)

const openFullScreen = () => {
  if (props.data.status === 1 || props.data.status === 3) return

  // On touch devices, first tap shows overlay, second tap opens fullscreen
  historyDetail.value = props.data as any
  showDetailModal.value = true
  // Update the URL to include the ID for navigation
  if (props.data.uuid) {
    router.push({ query: { uuid: props.data.uuid } })
  }
}

const closeFullScreen = () => {
  isFullScreenOpen.value = false
}

const showAudioResult = () => {
  showResult.value = true
}

const hideAudioResult = () => {
  showResult.value = false
}
</script>

<template>
  <!-- Card View -->
  <HistoryWrapper
    :id="data.id"
    :type="data.type"
    :status="data.status"
  >
    <UPageCard class="speech-card cursor-pointer h-full">
      <div class="p-0">
        <div
          class="mb-4 cursor-pointer"
          @click="openFullScreen"
        >
          <p
            class="text-xs text-gray-600 dark:text-gray-300 line-clamp-1 hover:text-primary-500 break-all"
          >
            {{ prompt }}
          </p>
        </div>

        <!-- Loading State -->
        <div
          v-if="loading || data.status === 1"
          class="w-full h-32 bg-gray-200 dark:bg-neutral-800 rounded-lg flex flex-col items-center justify-center mb-4"
        >
          <UIcon
            name="svg-spinners:blocks-wave"
            class="w-12 h-12 dark:text-neutral-700 mb-2"
          />
          <span class="text-xs text-gray-500 dark:text-gray-400">
            {{ $t('ui.messages.speechGenerating') }}
          </span>
        </div>

        <!-- Error State -->
        <div
          v-else-if="data?.status === 3"
          class="w-full h-32 text-error gap-2 bg-gray-200 dark:bg-neutral-800 rounded-lg flex flex-col items-center justify-center mb-4"
        >
          <UIcon
            name="material-symbols:error"
            class="w-8 h-8"
          />
          <div class="flex flex-col gap-1 items-center justify-center">
            <div class="text-xs">
              {{ $t("ui.errors.generationFailed") }}
            </div>
            <div class="text-xs px-4 text-error/70 text-center">
              {{ data?.error_message }}
            </div>
          </div>
        </div>

        <!-- Audio Result -->
        <div
          v-else-if="showResult && audioUrl"
          class="mb-4"
        >
          <div class="flex items-center justify-between mb-2">
            <span class="text-xs font-medium text-gray-700 dark:text-gray-300">
              {{ $t('ui.labels.generatedAudio') }}
            </span>
            <UButton
              icon="i-lucide-x"
              size="xs"
              color="neutral"
              variant="ghost"
              @click="hideAudioResult"
            />
          </div>
          <WaveformPlayer
            :audio-url="audioUrl"
            :fullscreen="false"
          />
        </div>

        <!-- Default Audio Player -->
        <div
          v-else
          class="mb-4"
        >
          <WaveformPlayer
            :audio-url="audioUrl"
            :fullscreen="false"
          />
        </div>

        <div
          class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400"
        >
          <span>{{ $t(model) }}</span>
          <UButton
            v-if="audioUrl && !showResult && data.status === 2"
            size="xs"
            color="primary"
            variant="soft"
            @click="showAudioResult"
          >
            {{ $t('ui.actions.showResult') }}
          </UButton>
        </div>

        <!-- Processing Alert -->
        <div
          v-if="data?.status === 1"
          class="mt-4"
        >
          <UAlert
            :title="$t('Tips')"
            :description="
              $t(
                'Your speech is still being generated in the background. You can close this page and check the history tab for the generated audio and we will notify you when it is ready.'
              )
            "
            color="neutral"
            variant="soft"
            icon="icon-park-twotone:info"
            :actions="[
              {
                label: $t('Go to History'),
                color: 'primary',
                variant: 'subtle',
                to: '/history'
              }
            ]"
          />
        </div>
      </div>
    </UPageCard>
  </HistoryWrapper>

  <!-- Full Screen Modal -->
  <UModal
    v-model:open="isFullScreenOpen"
    fullscreen
    :ui="{
      content: 'bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl'
    }"
    @keydown.esc="closeFullScreen"
  >
    <template #content>
      <div class="relative w-full h-full flex flex-col p-8">
        <!-- Close button -->
        <UButton
          icon="i-lucide-x"
          color="neutral"
          variant="ghost"
          class="absolute top-4 right-4 z-10"
          @click="closeFullScreen"
        />

        <!-- Content -->
        <div
          class="flex-1 flex flex-col justify-center max-w-4xl mx-auto w-full"
        >
          <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              {{ title }}
            </h2>
            <p class="text-lg text-gray-600 dark:text-gray-300 mb-6">
              {{ prompt }}
            </p>
            <div
              class="flex items-center justify-center gap-6 text-sm text-gray-500 dark:text-gray-400"
            >
              <span class="flex items-center gap-2">
                <UIcon name="i-lucide-cpu" />
                {{ model }}
              </span>
              <span class="flex items-center gap-2">
                <UIcon name="i-lucide-mic" />
                {{ voice }}
              </span>
            </div>
          </div>

          <div class="bg-white/50 dark:bg-neutral-800/50 rounded-lg p-8">
            <WaveformPlayer
              :audio-url="audioUrl"
              :fullscreen="true"
            />
          </div>
        </div>

        <div
          class="absolute bottom-4 left-4 text-gray-500 dark:text-gray-400 text-sm"
        >
          Press ESC to close
        </div>
      </div>
    </template>
  </UModal>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
