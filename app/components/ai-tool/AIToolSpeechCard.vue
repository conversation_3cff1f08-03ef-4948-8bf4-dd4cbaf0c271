<script setup lang="ts">
import { ref, computed } from 'vue'

const props = defineProps({
  orientation: {
    type: String as () => 'horizontal' | 'vertical',
    default: 'horizontal'
  },
  audioUrl: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  prompt: {
    type: String,
    default: ''
  },
  model: {
    type: String,
    default: 'Unknown'
  },
  voice: {
    type: String,
    default: ''
  },
  emotion: {
    type: String,
    default: ''
  },
  duration: {
    type: String,
    default: '0s'
  },
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const { models, model: currentModel } = useSpeechGenModels()
const textToSpeechStore = useTextToSpeechStore()
const { t } = useI18n()

const isFullscreen = ref(false)

const openFullscreen = () => {
  isFullscreen.value = true
}

const closeFullscreen = () => {
  isFullscreen.value = false
}

const downloadAudio = () => {
  if (props.audioUrl) {
    const link = document.createElement('a')
    link.href = props.audioUrl
    link.download = `speech-${Date.now()}.mp3`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

const regenerate = () => {
  textToSpeechStore.textToSpeech({
    input: props.prompt,
    model: currentModel.value.value,
    voices: [
      {
        name: props.voice,
        voice: {
          id: props.data?.voice_id,
          name: props.voice
        }
      }
    ],
    emotion: props.emotion,
    speed: 1.0,
    output_format: 'mp3',
    output_channel: 'mono'
  })
}

const cardClass = computed(() => {
  const baseClass = 'relative overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-sm hover:shadow-md transition-shadow duration-200'

  if (props.orientation === 'vertical') {
    return `${baseClass} flex flex-col`
  }

  return `${baseClass} flex flex-col lg:flex-row`
})

const contentClass = computed(() => {
  if (props.orientation === 'vertical') {
    return 'p-4 flex-1'
  }

  return 'p-4 flex-1 lg:w-1/2'
})

const audioClass = computed(() => {
  if (props.orientation === 'vertical') {
    return 'w-full'
  }

  return 'w-full lg:w-1/2'
})
</script>

<template>
  <UCard
    :class="cardClass"
    :ui="{
      body: 'p-0'
    }"
  >
    <div
      v-if="loading || data.status === 1"
      class="flex items-center justify-center h-64 bg-gray-50 dark:bg-gray-900"
    >
      <div class="text-center">
        <UIcon
          name="svg-spinners:blocks-wave"
          class="text-4xl text-primary mb-2"
        />
        <p class="text-sm text-gray-600 dark:text-gray-400">
          {{ t('ui.messages.speechGenerating') }}
        </p>
      </div>
    </div>

    <!-- Error State -->
    <div
      v-else-if="data?.status === 3"
      class="flex items-center justify-center h-64 bg-gray-50 dark:bg-gray-900"
    >
      <div class="text-center text-error">
        <UIcon
          name="material-symbols:error"
          class="text-4xl mb-2"
        />
        <div class="text-sm">
          {{ $t("ui.errors.generationFailed") }}
        </div>
        <div class="text-xs mt-1 px-4 text-error/70">
          {{ data?.error_message }}
        </div>
      </div>
    </div>

    <div
      v-else
      class="flex flex-col lg:flex-row h-full"
    >
      <!-- Audio Player Section -->
      <div :class="audioClass">
        <div class="p-4 h-full flex flex-col justify-center">
          <div class="flex items-center justify-center mb-4">
            <UIcon
              name="i-lucide-mic"
              class="text-6xl text-primary"
            />
          </div>

          <WaveformPlayer
            v-if="audioUrl"
            :audio-url="audioUrl"
            class="mb-4"
          />

          <div class="flex gap-2 justify-center">
            <UButton
              v-if="audioUrl"
              size="sm"
              variant="outline"
              icon="i-lucide-download"
              @click="downloadAudio"
            >
              {{ t('Download') }}
            </UButton>

            <UButton
              size="sm"
              variant="outline"
              icon="i-lucide-expand"
              @click="openFullscreen"
            >
              {{ t('View Details') }}
            </UButton>
          </div>
        </div>
      </div>

      <!-- Content Section -->
      <div :class="contentClass">
        <div class="h-full flex flex-col">
          <div class="flex-1">
            <h3 class="text-lg font-semibold mb-2 line-clamp-2">
              {{ title || t('Generated Speech') }}
            </h3>

            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
              {{ prompt }}
            </p>

            <div class="space-y-2 text-xs text-gray-500 dark:text-gray-400">
              <div class="flex justify-between">
                <span>{{ t('Model') }}:</span>
                <span class="font-medium">{{ model }}</span>
              </div>

              <div
                v-if="voice"
                class="flex justify-between"
              >
                <span>{{ t('Voice') }}:</span>
                <span class="font-medium">{{ voice }}</span>
              </div>

              <div
                v-if="emotion"
                class="flex justify-between"
              >
                <span>{{ t('Emotion') }}:</span>
                <span class="font-medium">{{ emotion }}</span>
              </div>

              <div class="flex justify-between">
                <span>{{ t('Duration') }}:</span>
                <span class="font-medium">{{ duration }}</span>
              </div>
            </div>
          </div>

          <!-- Processing Alert -->
          <div
            v-if="data?.status === 1"
            class="mt-4"
          >
            <UAlert
              :title="$t('Tips')"
              :description="
                $t(
                  'Your speech is still being generated in the background. You can close this page and check the history tab for the generated audio and we will notify you when it is ready.'
                )
              "
              color="neutral"
              variant="soft"
              icon="icon-park-twotone:info"
              :actions="[
                {
                  label: $t('Go to History'),
                  color: 'primary',
                  variant: 'subtle',
                  to: '/history'
                }
              ]"
            />
          </div>

          <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <UButton
              size="sm"
              color="primary"
              variant="soft"
              icon="i-lucide-refresh-cw"
              class="w-full"
              @click="regenerate"
            >
              {{ t('Regenerate Speech') }}
            </UButton>
          </div>
        </div>
      </div>
    </div>
  </UCard>

  <!-- Fullscreen Modal -->
  <UModal
    v-model="isFullscreen"
    :ui="{
      width: 'w-full max-w-6xl',
      height: 'h-full max-h-[90vh]'
    }"
  >
    <UCard
      :ui="{
        body: 'p-0',
        header: { padding: 'px-4 py-3 sm:px-6' },
        footer: { padding: 'px-4 py-3 sm:px-6' }
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold">
            {{ title || t('Generated Speech') }}
          </h3>
          <UButton
            icon="i-lucide-x"
            variant="ghost"
            size="sm"
            @click="closeFullscreen"
          />
        </div>
      </template>

      <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Audio Player -->
          <div class="flex flex-col items-center justify-center">
            <div class="mb-6">
              <UIcon
                name="i-lucide-mic"
                class="text-8xl text-primary"
              />
            </div>

            <WaveformPlayer
              v-if="audioUrl"
              :audio-url="audioUrl"
              :fullscreen="true"
              class="w-full"
            />
          </div>

          <!-- Information -->
          <div class="space-y-4">
            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                {{ t('Prompt') }}
              </h4>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ prompt }}
              </p>
            </div>

            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="font-medium text-gray-900 dark:text-gray-100">{{ t('Model') }}:</span>
                <p class="text-gray-600 dark:text-gray-400">{{ model }}</p>
              </div>

              <div v-if="voice">
                <span class="font-medium text-gray-900 dark:text-gray-100">{{ t('Voice') }}:</span>
                <p class="text-gray-600 dark:text-gray-400">{{ voice }}</p>
              </div>

              <div v-if="emotion">
                <span class="font-medium text-gray-900 dark:text-gray-100">{{ t('Emotion') }}:</span>
                <p class="text-gray-600 dark:text-gray-400">{{ emotion }}</p>
              </div>

              <div>
                <span class="font-medium text-gray-900 dark:text-gray-100">{{ t('Duration') }}:</span>
                <p class="text-gray-600 dark:text-gray-400">{{ duration }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex gap-2 justify-end">
          <UButton
            v-if="audioUrl"
            variant="outline"
            icon="i-lucide-download"
            @click="downloadAudio"
          >
            {{ t('Download') }}
          </UButton>

          <UButton
            color="primary"
            icon="i-lucide-refresh-cw"
            @click="regenerate"
          >
            {{ t('Regenerate Speech') }}
          </UButton>
        </div>
      </template>
    </UCard>
  </UModal>
</template>
