<script setup lang="ts">
import { ref, computed } from 'vue'

const props = defineProps({
  orientation: {
    type: String as () => 'horizontal' | 'vertical',
    default: 'vertical'
  },
  title: {
    type: String,
    default: ''
  },
  prompt: {
    type: String,
    default: ''
  },
  preset: {
    type: String,
    default: 'Unknown'
  },
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits<{
  'select-another-voice': []
}>()

const { models, model } = useSpeechGenModels()
const { t } = useI18n()

// Find the corresponding model label for the preset value
const presetLabel = computed(() => {
  const modelFound = models.find(m => m.value === props.data?.model_name)
  return modelFound ? modelFound.label : props.preset
})

const lastGenerated = computed(() => {
  return (
    props.data?.generated_audio?.[props.data?.generated_audio?.length - 1] || {}
  )
})

const audioUrl = computed(() => {
  return lastGenerated.value?.audio_url || ''
})

const onSelectAnotherVoice = () => {
  emit('select-another-voice')
}
</script>

<template>
  <UCard
    :ui="{
      body: 'h-full'
    }"
  >
    <div
      v-if="loading"
      class="flex flex-col items-center justify-center h-full"
    >
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4" />
      <div class="text-sm text-gray-600 dark:text-gray-400">
        {{ t('Generating dialog...') }}
      </div>
    </div>

    <div
      v-else-if="audioUrl || data"
      class="flex flex-col h-full"
    >
      <!-- Header -->
      <div class="flex items-center justify-between mb-4">
        <div class="flex-1 min-w-0">
          <h3 class="font-semibold text-gray-900 dark:text-white truncate">
            {{ title }}
          </h3>
          <div class="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400 mt-1">
            <span v-if="model">{{ model }}</span>
            <span v-if="model && duration">•</span>
            <span v-if="duration">{{ duration }}</span>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <UButton
            variant="ghost"
            size="sm"
            icon="i-lucide-expand"
            @click="openFullscreen"
          />
          <UButton
            variant="ghost"
            size="sm"
            icon="i-lucide-download"
            @click="downloadAudio"
          />
        </div>
      </div>

      <!-- Audio Player -->
      <div class="flex-1 mb-4">
        <WaveformPlayer
          v-if="audioUrl"
          :src="audioUrl"
          mode="card"
          class="w-full"
        />
        <div
          v-else
          class="flex items-center justify-center h-32 bg-gray-50 dark:bg-neutral-800 rounded-lg"
        >
          <UIcon
            name="i-lucide-users"
            class="text-4xl text-gray-400"
          />
        </div>
      </div>

      <!-- Dialog Info -->
      <div class="space-y-2 text-sm">
        <div
          v-if="voice1 || voice2"
          class="flex items-center gap-2"
        >
          <span class="text-gray-500 dark:text-gray-400">{{ t('Voices') }}:</span>
          <div class="flex items-center gap-2">
            <UBadge
              v-if="voice1"
              :label="voice1"
              variant="soft"
              color="primary"
              size="xs"
            />
            <UBadge
              v-if="voice2"
              :label="voice2"
              variant="soft"
              color="warning"
              size="xs"
            />
          </div>
        </div>
        <div
          v-if="emotion"
          class="flex items-center gap-2"
        >
          <span class="text-gray-500 dark:text-gray-400">{{ t('Emotion') }}:</span>
          <UBadge
            :label="emotion"
            variant="soft"
            color="gray"
            size="xs"
          />
        </div>
      </div>

      <!-- Actions -->
      <div class="flex justify-end gap-2 mt-4">
        <UButton
          variant="outline"
          size="sm"
          :label="t('Regenerate Dialog')"
          icon="i-lucide-refresh-cw"
          @click="handleRegenerate"
        />
      </div>
    </div>

    <div
      v-else
      class="flex flex-col items-center justify-center h-full"
    >
      <UIcon
        name="i-lucide-users"
        class="text-6xl text-gray-300 dark:text-gray-600 mb-2"
      />
      <div class="text-sm text-gray-500 dark:text-gray-400">
        {{ t('Your generated dialog will appear here') }}
      </div>
    </div>
  </UCard>

  <!-- Fullscreen Modal -->
  <UModal
    v-model="isFullscreen"
    :ui="{
      width: 'w-full max-w-6xl',
      height: 'h-full max-h-[90vh]'
    }"
  >
    <UCard
      :ui="{
        body: 'h-full',
        header: { padding: 'px-6 py-4' },
        footer: { padding: 'px-6 py-4' }
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
              {{ t('Generated Dialog') }}
            </h2>
            <div class="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mt-1">
              <span v-if="model">{{ model }}</span>
              <span v-if="model && duration">•</span>
              <span v-if="duration">{{ duration }}</span>
            </div>
          </div>
          <UButton
            variant="ghost"
            icon="i-lucide-x"
            @click="closeFullscreen"
          />
        </div>
      </template>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
        <!-- Left: Audio Player -->
        <div class="flex flex-col">
          <h3 class="text-lg font-medium mb-4">
            {{ t('Audio Player') }}
          </h3>
          <div class="flex-1">
            <WaveformPlayer
              v-if="audioUrl"
              :src="audioUrl"
              mode="fullscreen"
              class="w-full h-full"
            />
            <div
              v-else
              class="flex items-center justify-center h-full bg-gray-50 dark:bg-neutral-800 rounded-lg"
            >
              <UIcon
                name="i-lucide-users"
                class="text-6xl text-gray-400"
              />
            </div>
          </div>
        </div>

        <!-- Right: Information -->
        <div class="flex flex-col">
          <h3 class="text-lg font-medium mb-4">
            {{ t('Dialog Information') }}
          </h3>
          <div class="space-y-4 flex-1">
            <div
              v-if="prompt"
              class="space-y-2"
            >
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('Style Description') }}
              </label>
              <div class="p-3 bg-gray-50 dark:bg-neutral-800 rounded-lg text-sm">
                {{ prompt }}
              </div>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div
                v-if="voice1"
                class="space-y-2"
              >
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ t('Voice 1') }}
                </label>
                <UBadge
                  :label="voice1"
                  variant="soft"
                  color="primary"
                />
              </div>
              <div
                v-if="voice2"
                class="space-y-2"
              >
                <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ t('Voice 2') }}
                </label>
                <UBadge
                  :label="voice2"
                  variant="soft"
                  color="warning"
                />
              </div>
            </div>

            <div
              v-if="emotion"
              class="space-y-2"
            >
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('Emotion') }}
              </label>
              <UBadge
                :label="emotion"
                variant="soft"
                color="gray"
              />
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-between">
          <UButton
            variant="outline"
            :label="t('Download')"
            icon="i-lucide-download"
            @click="downloadAudio"
          />
          <div class="flex gap-2">
            <UButton
              variant="outline"
              :label="t('Regenerate Dialog')"
              icon="i-lucide-refresh-cw"
              @click="handleRegenerate"
            />
            <UButton
              :label="t('Close')"
              @click="closeFullscreen"
            />
          </div>
        </div>
      </template>
    </UCard>
  </UModal>
</template>
