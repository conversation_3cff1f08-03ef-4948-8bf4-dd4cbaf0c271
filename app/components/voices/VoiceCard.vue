<template>
  <UCard
    :ui="{
      body: '!p-0'
    }"
    v-bind="$attrs"
    class="group"
  >
    <div
      class="flex flex-row items-center"
      :class="{ 'px-3 py-1': mini, 'p-3': !mini }"
    >
      <div
        class="h-10 w-10 bg-neutral-800 rounded-full relative justify-between items-center"
      >
        <UIcon
          :name="voice.icon"
          class="w-7 h-7 text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
        />
        <div
          class="group-hover:flex cursor-pointer bg-neutral-800/90 z-10 h-10 w-10 rounded-full"
          :class="{
            flex: isPlaying,
            hidden: !isPlaying
          }"
          @click.stop="$emit('play-preview', voice)"
        >
          <UIcon
            v-if="isPlaying"
            name="solar:pause-bold"
            class="w-4 h-4 text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          />
          <UIcon
            v-else
            name="solar:play-bold"
            class="w-4 h-4 text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          />
        </div>
      </div>
      <div class="text-xs">
        {{ props.voice.speaker_name }}
      </div>
    </div>
  </UCard>
</template>

<script setup lang="ts">
import type { SpeechVoice } from '~/composables/useSpeechVoices'

interface VoiceCardProps {
  voice: SpeechVoice
  isPlaying?: boolean
  mini?: boolean
}

const props = defineProps<VoiceCardProps>()

const { getAccentByValue } = useSpeechVoices()
</script>
